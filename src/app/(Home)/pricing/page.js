"use client";

import { Col, Container, Row } from "react-bootstrap";
import CommonButton from "@/Components/UI/CommonButton";
import Link from "next/link";
import { CheckIcon, RedCrossIcon } from "@/assets/svgIcons/SvgIcon";
import { useRouter } from "next/navigation";
import Switch from "@/Components/UI/Switch";
import FaqCard from "@/Components/common/Home/FaqCard";
import HomeLayout from "@/Layouts/HomeLayout";
import "@/css/Home/Pricing.scss";
import { useEffect, useState } from "react";
import MetaHead from "@/Seo/Meta/MetaHead";
import { usePathname, useSearchParams } from "next/navigation";
import Cookies from "js-cookie";
import Loader from "@/Components/common/Loader";
import { loadStripe } from '@stripe/stripe-js';




const Pricing = () => {
  const [loginToken, setLoginToken] = useState(null);
  const router = useRouter();
  const [isMonthly, setIsMonthly] = useState(false);
  const [isFree, setisFree] = useState(false);
  const [currentPlan, setCurrentPlan] = useState("Free");
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [plans, setPlans] = useState({});
  const [billingPlans, setBillingPlans] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  


  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const tokens = Cookies.get("authToken");
    setLoginToken(tokens);

    if (tokens) {
      setIsLoggedIn(true);
      const storedPlan = sessionStorage.getItem("plan");
      if (storedPlan) {
        setCurrentPlan(storedPlan);
      }
      setisFree(currentPlan === "Free");
    } else {
      setIsLoggedIn(false);
    }
    setIsLoading(false); // ✅ only after logic is complete

  }, [currentPlan]);

  const handlePlanChange = (plan) => {
    setCurrentPlan(plan);
    sessionStorage.setItem("plan", plan);
    if (plan !== "Free") {
      setisFree(false);
    }
  };

  const handleSwitchplan = () => {
  setIsMonthly(prev => {
    const newIsMonthly = !prev;

    const filteredPlans = plans?.filter(plan =>
      plan?.billing_type === (newIsMonthly ? "monthly" : "yearly")
    );
    setBillingPlans(filteredPlans);

    return newIsMonthly;
  });
};


  useEffect(() => {
    sessionStorage.removeItem("pricing");
    sessionStorage.removeItem("trial");

    Cookies.remove('first_login');
    Cookies.remove('returning_login');

    const hasExtraPath = pathname !== "/pricing";
    const hasQueryParams = searchParams.toString().length > 0;

    if (hasExtraPath || hasQueryParams) {
      setisFree(true);
    }
  }, []);

  useEffect(() => {
    const tokens = Cookies.get("authToken");
    setLoginToken(tokens);
  }, []);

   useEffect(() => {
    const fetchPricingPlans = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/stripe/plans?billing_type=${isMonthly ? 'monthly' : 'yearly'}`);
        if (!response.ok) {
          throw new Error("Failed to fetch plans");
        }
        const { data } = await response.json();
        setPlans(data);
        const yearlyPlans = data?.filter(plan => plan?.billing_type === "yearly");
        setBillingPlans(yearlyPlans);
      } catch (error) {
        console.error("Error fetching plans:", error);
      }
    };
    fetchPricingPlans();
  }, []);



  const priceTable = [
    {
      title: "Essential",
      priceyear: "12.95",
      billed_annually: "$155.40 Billed Annually",
      price: "14.95",
      discount: "Savings: $24 Annually",
      features: {
        feature_allowed: [
          "No Ads",
          "3 Trade Accounts",
          "Trade History Retention (Unlimited)",
          "10 Dashboards",
          "25 Strategy Limit",
          "KPI Widget Types (Unlimited)",
          "10 Dashboard Presets",
          "10 Strategy Presets",
          "Sync Brokers",
          "CSV Trade Import",
          "Marketplace Access",
          "Upload Chart Images",
          "25 Market Replay Limit",
          "Analytics Report Access",
          "10 Analytics Saved Views",
          "Custom Tags (Unlimited)",
          "Trade Notes",
          "Trade Builder Extra Fields: 9",
          "Refer A Friend Access",
          "Affiliate Partner Access",
          "Community Access",
          "Achievements",
          "Standard Support (Ticket-based)",
          "Marketplace Seller Privileges",
          "Marketplace Seller Fee: 5%"
        ],
        feature_not_allowed: [
          "Priority Support"
        ]
      }
    },
    {
      title: "Plus",
      priceyear: "24.95",
      billed_annually: "$299.40 Billed Annually",
      price: "29.95",
      discount: "Savings: $60 Annually",
      features: {
        feature_allowed: [
          "No Ads",
          "10 Trade Accounts",
          "Trade History Retention (Unlimited)",
          "25 Dashboards",
          "Strategy Limit (Unlimited)",
          "KPI Widget Types (Unlimited)",
          "25 Dashboard Presets",
          "25 Strategy Presets",
          "Sync Brokers",
          "CSV Trade Import",
          "Marketplace Access",
          "Upload Chart Images",
          "100 Market Replay Limit",
          "Analytics Report Access",
          "25 Analytics Saved Views",
          "Custom Tags (Unlimited)",
          "Trade Notes",
          "Trade Builder Extra Fields: 24",
          "Refer A Friend Access",
          "Affiliate Partner Access",
          "Community Access",
          "Achievements",
          "Standard Support (Ticket-based)",
          "Marketplace Seller Privileges",
          "Marketplace Seller Fee: 3%"
        ],
        feature_not_allowed: ["Priority Support"]
      }
    },
    {
      title: "Premium",
      priceyear: "39.95",
      billed_annually: "$479.40 Billed Annually",
      price: "49.95",
      discount: "Savings: $120 Annually",
      features: {
        feature_allowed: [
          "No Ads",
          "25 Trade Accounts",
          "Trade History Retention (Unlimited)",
          "100 Dashboards",
          "Strategy Limit (Unlimited)",
          "KPI Widget Types (Unlimited)",
          "50 Dashboard Presets",
          "50 Strategy Presets",
          "Sync Brokers",
          "CSV Trade Import",
          "Marketplace Access",
          "Upload Chart Images",
          "Market Replay Limit (Unlimited)",
          "Analytics Report Access",
          "Analytics Saved Views (Unlimited)",
          "Custom Tags (Unlimited)",
          "Trade Notes",
          "Trade Builder Extra Fields: 45",
          "Refer A Friend Access",
          "Affiliate Partner Access",
          "Community Access",
          "Achievements",
          "Standard Support (Priority Support)",
          "Marketplace Seller Privileges",
          "Marketplace Seller Fee: 1%",
          "Priority Support (Faster Response Time)"
        ],
        feature_not_allowed: []
      }
    }
  ];


  // stripe api call to send the id and get session-id then moved to checkout stripe url
  const handleSubscribe = async (planId) => {
    if (!loginToken) {
      router.push("/signup");
      return;
    }
  
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/subscribe`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${loginToken}`, // Add this if required
        },
        body: JSON.stringify({ plan_id: planId }),
      });
  
      const data = await response.json();
  
      if (data?.session_id) {
        const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY);
        await stripe.redirectToCheckout({ sessionId: data.session_id });
      } else {
        throw new Error("No session_id in response");
      }
    } catch (error) {
      console.error("Checkout error:", error);
      alert("Failed to redirect to checkout. Please try again.");
    }
  };

  // upper APis when I am doing payemnt moved to https://dev.tradereply.com/pricing?session_id=cs_test_a1VkvMV5Dkqzue9WVT3Rp9LHGhsy8BaefeKOU5Gdv6I8dA1VkNSvRTz79V
  // now I am getting this session id 

  useEffect(() => {
    const sessionId = searchParams.get("session_id");
  
    if (sessionId && loginToken) {
      const confirmSubscription = async () => {
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/confirm/subscribe`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${loginToken}`,
              },
              body: JSON.stringify({ session_id: sessionId }),
            }
          );
  
          if (!response.ok) {
            throw new Error("Subscription confirmation failed");
          }
  
          const data = await response.json();
          console.log("Subscription confirmed:", data);
  
          // ✅ Assuming your backend returns plan details like this
          const newPlan = data?.plan_name || "Premium"; // fallback
          setCurrentPlan(newPlan);
          sessionStorage.setItem("plan", newPlan);
  
          alert("Payment successful! Your subscription is now active.");
          // Optionally, remove session_id from URL (clean up)
          const url = new URL(window.location.href);
          url.searchParams.delete("session_id");
          window.history.replaceState({}, "", url.toString());
        } catch (error) {
          console.error("Error confirming subscription:", error);
          alert("Something went wrong while confirming your payment.");
        }
      };
  
      confirmSubscription();
    }
  }, [searchParams]);
    


  const metaArray = {
    title: "TradeReply Pricing Plans | Choose Your Trading Tools",
    description: "Discover flexible pricing plans at TradeReply.com. Choose the perfect plan to access advanced trading tools, real-time analytics, and strategy optimization.",
    canonical_link: "https://www.tradereply.com/pricing",
    og_site_name: "TradeReply",
    og_title: "TradeReply Pricing Plans | Choose Your Trading Tools",
    og_description: "Discover flexible pricing plans at TradeReply.com. Choose the perfect plan to access advanced trading tools, real-time analytics, and strategy optimization.",
    twitter_title: "TradeReply Pricing Plans | Choose Your Trading Tools",
    twitter_description: "Discover flexible pricing plans at TradeReply.com. Choose the perfect plan to access advanced trading tools, real-time analytics, and strategy optimization.",
  };
  // if (isLoading) return null;
  return (
    <HomeLayout>
      <MetaHead props={metaArray} />

      <div className="pricing">

        <section className="pricing_banner">
          <Container>
            {isLoading ? (
              <div>
                <Loader />
              </div>
            ) : (
              <Row className="gx-xl-5 align-items-center justify-content-center">
                <Col md={isFree ? 12 : 7} xs={12} xl={isFree ? 12 : 8}>
                  <div className="pricing_banner_content text-center">
                    <h1>
                      {isLoggedIn
                        ? currentPlan === "Free"
                          ? "Try any of our plans, free for 30 days"
                          : "Manage your plan or explore additional features"
                        : "Join Free, Upgrade Anytime"}
                    </h1>

                    <p>
                      {currentPlan === "Free" && !isLoggedIn
                        ? "Enjoy powerful insights with our free analytics suite. Upgrade anytime for additional premium features."
                        : currentPlan === "Essential"
                          ? "You’re currently on the Essential plan. Unlock more advanced tools by upgrading to Plus or Premium."
                          : currentPlan === "Plus"
                            ? "You’re currently on the Plus plan. Want even deeper insights and automation? Upgrade to Premium anytime."
                            : currentPlan === "Premium"
                              ? "You’re on our most advanced plan — Premium. Looking to downgrade or adjust your billing preferences?"
                              : ""}
                    </p>
                  </div>
                </Col>

                {!isFree && (
                  <Col md={5} xs={12} xl={4}>
                    <div className="pricing_banner_forever">
                      <h3>$0 forever</h3>
                      <div>
                        <CommonButton
                          onClick={() => {
                            sessionStorage.setItem("pricing", "free");
                            sessionStorage.setItem("trial", "false");
                            router.push("/signup");
                          }}
                          title="Join Free"
                          className="gradient-btn my-3 my-md-4"
                        />
                      </div>
                      <h4>No Credit Card Required</h4>
                    </div>
                  </Col>
                )}
              </Row>
            )}

          </Container>
        </section>



        <section className="pricing_table">
          <Container>
            <div className="pricing_table_switch d-flex align-items-center justify-content-center">
              <p>Monthly</p>
              <Switch
                checked={Boolean(!isMonthly)}
                onChange={handleSwitchplan}
              />
              <p>Annually</p>
            </div>
            <Row className="gx-0 gy-4">
              {billingPlans?.map((item, index) => {
                console.log("itemssss",item);
                const planName = item?.title;
                const planOrder = ["Free", "Essential", "Plus", "Premium"];
                const currentIndex = planOrder.indexOf(currentPlan);
                const thisIndex = planOrder.indexOf(planName);

                let buttonTitle = "";
                let buttonClass = "";
                let showButton = true;

                if (planName === currentPlan) {
                  buttonTitle = "Current Plan";
                  buttonClass = "gray-btn";
                } else if (currentPlan === "Free") {
                  buttonTitle = "Try Free for 30 Days";
                  buttonClass = "green-btn free_for";
                } else if (thisIndex > currentIndex) {
                  buttonTitle = `Upgrade to ${planName}`;
                  buttonClass = "green-btn";
                } else if (thisIndex < currentIndex) {
                  buttonTitle = `Downgrade to ${planName}`;
                  buttonClass = "yellow-btn";
                }

                return (
                  <Col lg={4} xs={12} key={index} className="pricing_table_col d-flex">
                    <div className="pricing_table_box w-100">
                      <div className="pricing_table_box_heading">
                        <h3>{item?.title}</h3>
                        <h2>
                          ${item?.price}
                          <span> /${isMonthly ? 'month' : 'year'} </span>
                        </h2>
                        {!isMonthly && (
                          <>
                            <p>{item?.billed_description || ''}</p>
                            <p>({item?.discount || ''})</p>
                          </>
                        )}

                        {showButton && (
                          <CommonButton
                            // onClick={() => {
                            //   // sessionStorage.setItem("pricing", planName.toLowerCase());
                            //   // sessionStorage.setItem("trial", currentPlan === "Free" ? "true" : "false");
                            //   // router.push("/signup");
                            // }}
                            onClick={() => handleSubscribe(item?.id)}
                            title={buttonTitle}
                            className={`btn-style ${buttonClass}`}
                          />
                        )}

                        {!loginToken && (
                          <p>
                            No Trial Needed?{" "}
                            <Link
                              href="/signup"
                              onClick={() => {
                                sessionStorage.setItem("pricing", planName.toLowerCase());
                                sessionStorage.setItem("trial", "false");
                              }}
                              className="text-blue-500"
                            >
                              Pay Now
                            </Link>
                          </p>
                        )}
                      </div>

                       <ul>
                        {item?.rules?.map((rule, i) => (
                          <li className="d-flex gap-2 align-items-center" key={i}>
                            {rule.is_allowed ? (
                              <CheckIcon width="20" height="20" />
                            ) : (
                              <RedCrossIcon />
                            )}
                            <span>{rule.description}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </Col>
                );
              })}

            </Row>
          </Container>
        </section>

        <div className="py-50">
          <Container>
            <FaqCard isPricing={true} />
          </Container>
        </div>
      </div>
    </HomeLayout>
  );
};

export default Pricing;
