name: Deploy Frontend Dev to EC2

on:
  pull_request:
    branches:
      - dev
  push:
    branches:
      - dev

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      AWS_REGION: us-east-1 
      NODE_ENV: development

    steps:
      # 1. Checkout the repository
      - name: Checkout Code
        uses: actions/checkout@v3

      # 2. Configure AWS Credentials
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          aws-region: ${{ env.AWS_REGION }}

      # 3. Set up SSH key for EC2 access
      - name: Set up SSH Key
        run: |
          echo "${{ secrets.EC2_KEY_FRONTEND_DEV }}" | base64 -d > ec2_key.pem
          chmod 600 ec2_key.pem

      # 4. Deploy to EC2 via SSH
      - name: Deploy to EC2 via SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.EC2_HOST_FRONTEND_DEV }}
          username: ubuntu  
          key_path: ec2_key.pem
          script: |
            cd /home/<USER>/frontend
            git reset --hard origin/dev
            git pull origin dev
            npm install --force
            sudo systemctl restart tradereply.service

      # 5. Clean up the SSH key after deployment
      - name: Clean up the SSH key
        if: always()
        run: |
         shred -u ec2_key.pem
