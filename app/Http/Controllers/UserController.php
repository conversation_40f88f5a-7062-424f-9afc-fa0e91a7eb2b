<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Rules\NoProfanity;
use App\Rules\ValidUsername;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    //
    use ApiResponseTrait;

    public function index()
    {
        $user = auth()->user();

        $userData = $user->only([
            'id',
            'name',
            'email',
            'first_name',
            'last_name',
            'phone_number',
            'country',
            'language',
            'timezone',
            'currency',
            'number_format',
            'username',
            'two_factor_enabled',
            'email_verified_at'
        ]);

        // Add computed security status fields
        $userData['is_email_verified'] = !is_null($user->email_verified_at);
        $userData['is_two_factor_enabled'] = (bool) $user->two_factor_enabled;
        $userData['has_phone_number'] = !is_null($user->phone_number) && !empty($user->phone_number);

        return $this->successResponse(
            $userData,
            'User personal information fetched successfully'
        );
    }


    public function update(User $user, Request $request)
    {

//        $checkUniqueness = filter_var($request->flag, FILTER_VALIDATE_BOOLEAN);

        $request->validate([
            'first_name' => 'nullable|string|max:50',
            'last_name' => 'nullable|string|max:50',
            'phone_number' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'language' => ['nullable', 'string', Rule::in(config('account_settings.languages'))],
            'timezone' => ['nullable', 'string', Rule::in(config('account_settings.timezones'))],
            'currency' => ['nullable', 'string', Rule::in(config('account_settings.currencies'))],
            'number_format' => ['nullable', 'string', Rule::in(config('account_settings.number_formats'))],
//            'username' => [
//                'string',
//                'max:20',
//                new ValidUsername,
//                new NoProfanity,
//            ],
            ]);

//        if ($checkUniqueness) {
//            $rules['username'][] = 'required';
//            $rules['username'][] = 'unique:users,username';
//        } else {
//            $rules['username'][] = 'nullable';
//        }

//        $messages = [
//            'username.unique' => 'This username is already taken.',
//        ];


        $data = $request->only([
            'first_name',
            'last_name',
            'phone_number',
            'country',
            'language',
            'timezone',
            'currency',
            'number_format',
            'email',
//            'username'
        ]);

        if ($user->id !== auth()->id()) {
            abort(403, 'Unauthorized');
        }

        if ($request->filled('email')) {
            if (strtolower($request->email) !== strtolower(auth()->user()->email)) {
                return response()->json(['message' => 'Current email does not match.'], 422);
            }

            if (strtolower($request->new_email) === strtolower($user->email)) {
                return response()->json(['message' => 'Kindly add a new email to update.'], 422);
            }

            $token = $this->generateToken();

            Cache::put(
                'email_change_' . $user->username,
                [
                    'new_email' => $request->new_email,
                    'old_email' => $user->email,
                    'token' => $token,
                ],
                now()->addMinutes(30)
            );

            return response()->json(['message' => 'Verification code sent to new email.', 'token' => $token]); // for testing only
        }

//        if (isset($data['username']) && $data['username'] !== $user->username) {
//            if ($user->username_change_count >= 2) {
//                return response()->json(['message' => 'Username change limit reached.'], 422);
//            }
//            $user->username_change_count += 1;
//        }

        if (isset($data['phone_number'])) {
            $data['phone_number'] = preg_replace('/\D/', '', $data['phone_number']);
        }

        $user->fill($data);
        $user->save();

        return response()->json([
            'message' => 'Account details updated successfully.',
            'user' => $user->fresh()
        ]);
    }


    public function updateEmail(User $user, Request $request)
    {
        $request->validate([
            'token' => 'required|string'
        ]);

        if ($user->id !== auth()->id()) {
            abort(403, 'Unauthorized');
        }

        $cacheData = Cache::get('email_change_' . $user->username);

        if (!$cacheData || $cacheData['token'] !== $request->token) {
            return response()->json(['message' => 'Invalid or expired token.'], 422);
        }

        $user->email = $cacheData['new_email'];
        $user->save();

        Cache::forget('email_change_' . $user->username);

        return response()->json(['message' => 'Email updated successfully.']);
    }


    private function generateToken()
    {
        $characters = 'ACDEFGHJKMNPQRTUVWXYZ234679';
        return substr(str_shuffle(str_repeat($characters, 6)), 0, 6);
    }

}
